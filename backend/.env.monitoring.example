# Monitoring and Alerting Configuration
# Copy this file to .env and configure the values for your environment

# =============================================================================
# ALERT THRESHOLDS CONFIGURATION
# =============================================================================

# Response Time Thresholds (in milliseconds)
ALERT_RESPONSE_TIME_WARNING=1000
ALERT_RESPONSE_TIME_CRITICAL=5000

# Error Rate Thresholds (percentage)
ALERT_ERROR_RATE_WARNING=5.0
ALERT_ERROR_RATE_CRITICAL=10.0

# Memory Usage Thresholds (percentage)
ALERT_MEMORY_WARNING=80.0
ALERT_MEMORY_CRITICAL=90.0

# CPU Load Thresholds (percentage)
ALERT_CPU_WARNING=75.0
ALERT_CPU_CRITICAL=90.0

# Database Response Time Thresholds (in milliseconds)
ALERT_DB_RESPONSE_WARNING=500
ALERT_DB_RESPONSE_CRITICAL=2000

# Disk Usage Thresholds (percentage)
ALERT_DISK_WARNING=80.0
ALERT_DISK_CRITICAL=90.0

# =============================================================================
# EMAIL NOTIFICATION CONFIGURATION
# =============================================================================

# SMTP Server Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false

# Email Authentication
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password

# Email Addresses
EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# =============================================================================
# SLACK NOTIFICATION CONFIGURATION
# =============================================================================

# Slack Webhook URL (create in Slack App settings)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Slack Channel and Bot Configuration
SLACK_CHANNEL=#alerts
SLACK_USERNAME=Adopte un Étudiant Monitor

# =============================================================================
# MONITORING INTERVALS CONFIGURATION
# =============================================================================

# Health Check Interval (in milliseconds)
HEALTH_CHECK_INTERVAL=30000

# Metrics Update Interval (in milliseconds)
METRICS_UPDATE_INTERVAL=60000

# Business Metrics Update Interval (in milliseconds)
BUSINESS_METRICS_INTERVAL=300000

# External Services Check Interval (in milliseconds)
EXTERNAL_SERVICES_INTERVAL=120000

# =============================================================================
# ALERT THROTTLING CONFIGURATION
# =============================================================================

# Alert Throttling Times (in milliseconds)
ALERT_THROTTLE_INFO=1800000      # 30 minutes
ALERT_THROTTLE_WARNING=900000    # 15 minutes
ALERT_THROTTLE_CRITICAL=300000   # 5 minutes

# =============================================================================
# MONITORING FEATURES CONFIGURATION
# =============================================================================

# Enable/Disable Monitoring Features
MONITORING_ENABLED=true
METRICS_COLLECTION_ENABLED=true
ALERTING_ENABLED=true
HEALTH_CHECKS_ENABLED=true

# Monitoring Dashboard Configuration
MONITORING_DASHBOARD_ENABLED=true
MONITORING_DASHBOARD_PATH=/api/status

# =============================================================================
# EXTERNAL SERVICES MONITORING
# =============================================================================

# Enable external service monitoring
EXTERNAL_MONITORING_ENABLED=true

# Elasticsearch Monitoring (if using Elasticsearch logging)
ELASTICSEARCH_MONITORING_ENABLED=true

# Email Service Monitoring
EMAIL_SERVICE_MONITORING_ENABLED=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Metrics Endpoint Security (set to true to require authentication)
METRICS_REQUIRE_AUTH=false

# Health Check Endpoint Security (set to true to require authentication)
HEALTH_CHECK_REQUIRE_AUTH=false

# Monitoring Dashboard Security (set to true to require authentication)
DASHBOARD_REQUIRE_AUTH=false

# =============================================================================
# LOGGING CONFIGURATION FOR MONITORING
# =============================================================================

# Monitoring Log Level (debug, info, warn, error)
MONITORING_LOG_LEVEL=info

# Enable detailed monitoring logs
MONITORING_DETAILED_LOGS=false

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Metrics Collection Sampling Rate (0.0 to 1.0)
METRICS_SAMPLING_RATE=1.0

# Maximum Metrics History (number of data points to keep)
METRICS_MAX_HISTORY=1000

# Alert Queue Size (maximum pending alerts)
ALERT_QUEUE_SIZE=100

# =============================================================================
# DEVELOPMENT/TESTING CONFIGURATION
# =============================================================================

# Enable test alerts (for testing the alert system)
TEST_ALERTS_ENABLED=false

# Mock external services (for development/testing)
MOCK_EXTERNAL_SERVICES=false

# Disable alerts in development
DISABLE_ALERTS_IN_DEV=true

# =============================================================================
# EXAMPLE PRODUCTION CONFIGURATION
# =============================================================================

# For production deployment, use these recommended values:
# NODE_ENV=production
# ALERT_RESPONSE_TIME_WARNING=2000
# ALERT_RESPONSE_TIME_CRITICAL=10000
# ALERT_ERROR_RATE_WARNING=2.0
# ALERT_ERROR_RATE_CRITICAL=5.0
# ALERT_MEMORY_WARNING=85.0
# ALERT_MEMORY_CRITICAL=95.0
# MONITORING_LOG_LEVEL=warn
# MONITORING_DETAILED_LOGS=false
# METRICS_SAMPLING_RATE=0.1
# ALERTING_ENABLED=true

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env in the backend directory
# 2. Configure your email SMTP settings
# 3. Set up Slack webhook URL (optional)
# 4. Adjust alert thresholds based on your requirements
# 5. Test the configuration using the test endpoints
# 6. Monitor the logs to ensure everything is working correctly

# Test the configuration:
# curl http://localhost:3001/api/health
# curl http://localhost:3001/api/metrics
# curl http://localhost:3001/api/status
